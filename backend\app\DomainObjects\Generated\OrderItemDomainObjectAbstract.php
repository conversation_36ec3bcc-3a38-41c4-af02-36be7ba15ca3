<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class OrderItemDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'order_item';
    final public const PLURAL_NAME = 'order_items';
    final public const ID = 'id';
    final public const ORDER_ID = 'order_id';
    final public const PRODUCT_ID = 'product_id';
    final public const PRODUCT_PRICE_ID = 'product_price_id';
    final public const TOTAL_BEFORE_ADDITIONS = 'total_before_additions';
    final public const QUANTITY = 'quantity';
    final public const ITEM_NAME = 'item_name';
    final public const PRICE = 'price';
    final public const PRICE_BEFORE_DISCOUNT = 'price_before_discount';
    final public const DELETED_AT = 'deleted_at';
    final public const TOTAL_TAX = 'total_tax';
    final public const TOTAL_GROSS = 'total_gross';
    final public const TOTAL_SERVICE_FEE = 'total_service_fee';
    final public const TAXES_AND_FEES_ROLLUP = 'taxes_and_fees_rollup';
    final public const PRODUCT_TYPE = 'product_type';

    protected int $id;
    protected int $order_id;
    protected int $product_id;
    protected int $product_price_id;
    protected float $total_before_additions;
    protected int $quantity;
    protected ?string $item_name = null;
    protected float $price;
    protected ?float $price_before_discount = null;
    protected ?string $deleted_at = null;
    protected float $total_tax = 0.0;
    protected ?float $total_gross = null;
    protected ?float $total_service_fee = 0.0;
    protected array|string|null $taxes_and_fees_rollup = null;
    protected string $product_type = 'TICKET';

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'order_id' => $this->order_id ?? null,
                    'product_id' => $this->product_id ?? null,
                    'product_price_id' => $this->product_price_id ?? null,
                    'total_before_additions' => $this->total_before_additions ?? null,
                    'quantity' => $this->quantity ?? null,
                    'item_name' => $this->item_name ?? null,
                    'price' => $this->price ?? null,
                    'price_before_discount' => $this->price_before_discount ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                    'total_tax' => $this->total_tax ?? null,
                    'total_gross' => $this->total_gross ?? null,
                    'total_service_fee' => $this->total_service_fee ?? null,
                    'taxes_and_fees_rollup' => $this->taxes_and_fees_rollup ?? null,
                    'product_type' => $this->product_type ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setOrderId(int $order_id): self
    {
        $this->order_id = $order_id;
        return $this;
    }

    public function getOrderId(): int
    {
        return $this->order_id;
    }

    public function setProductId(int $product_id): self
    {
        $this->product_id = $product_id;
        return $this;
    }

    public function getProductId(): int
    {
        return $this->product_id;
    }

    public function setProductPriceId(int $product_price_id): self
    {
        $this->product_price_id = $product_price_id;
        return $this;
    }

    public function getProductPriceId(): int
    {
        return $this->product_price_id;
    }

    public function setTotalBeforeAdditions(float $total_before_additions): self
    {
        $this->total_before_additions = $total_before_additions;
        return $this;
    }

    public function getTotalBeforeAdditions(): float
    {
        return $this->total_before_additions;
    }

    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;
        return $this;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function setItemName(?string $item_name): self
    {
        $this->item_name = $item_name;
        return $this;
    }

    public function getItemName(): ?string
    {
        return $this->item_name;
    }

    public function setPrice(float $price): self
    {
        $this->price = $price;
        return $this;
    }

    public function getPrice(): float
    {
        return $this->price;
    }

    public function setPriceBeforeDiscount(?float $price_before_discount): self
    {
        $this->price_before_discount = $price_before_discount;
        return $this;
    }

    public function getPriceBeforeDiscount(): ?float
    {
        return $this->price_before_discount;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }

    public function setTotalTax(float $total_tax): self
    {
        $this->total_tax = $total_tax;
        return $this;
    }

    public function getTotalTax(): float
    {
        return $this->total_tax;
    }

    public function setTotalGross(?float $total_gross): self
    {
        $this->total_gross = $total_gross;
        return $this;
    }

    public function getTotalGross(): ?float
    {
        return $this->total_gross;
    }

    public function setTotalServiceFee(?float $total_service_fee): self
    {
        $this->total_service_fee = $total_service_fee;
        return $this;
    }

    public function getTotalServiceFee(): ?float
    {
        return $this->total_service_fee;
    }

    public function setTaxesAndFeesRollup(array|string|null $taxes_and_fees_rollup): self
    {
        $this->taxes_and_fees_rollup = $taxes_and_fees_rollup;
        return $this;
    }

    public function getTaxesAndFeesRollup(): array|string|null
    {
        return $this->taxes_and_fees_rollup;
    }

    public function setProductType(string $product_type): self
    {
        $this->product_type = $product_type;
        return $this;
    }

    public function getProductType(): string
    {
        return $this->product_type;
    }
}
