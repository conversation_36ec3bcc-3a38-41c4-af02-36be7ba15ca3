<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class InvoiceDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'invoice';
    final public const PLURAL_NAME = 'invoices';
    final public const ID = 'id';
    final public const ORDER_ID = 'order_id';
    final public const ACCOUNT_ID = 'account_id';
    final public const INVOICE_NUMBER = 'invoice_number';
    final public const ISSUE_DATE = 'issue_date';
    final public const DUE_DATE = 'due_date';
    final public const TOTAL_AMOUNT = 'total_amount';
    final public const STATUS = 'status';
    final public const ITEMS = 'items';
    final public const TAXES_AND_FEES = 'taxes_and_fees';
    final public const UUID = 'uuid';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';
    final public const DELETED_AT = 'deleted_at';

    protected int $id;
    protected int $order_id;
    protected int $account_id;
    protected string $invoice_number;
    protected string $issue_date = 'CURRENT_TIMESTAMP';
    protected ?string $due_date = null;
    protected float $total_amount;
    protected string $status = 'PENDING';
    protected array|string $items;
    protected array|string|null $taxes_and_fees = null;
    protected string $uuid;
    protected ?string $created_at = null;
    protected ?string $updated_at = null;
    protected ?string $deleted_at = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'order_id' => $this->order_id ?? null,
                    'account_id' => $this->account_id ?? null,
                    'invoice_number' => $this->invoice_number ?? null,
                    'issue_date' => $this->issue_date ?? null,
                    'due_date' => $this->due_date ?? null,
                    'total_amount' => $this->total_amount ?? null,
                    'status' => $this->status ?? null,
                    'items' => $this->items ?? null,
                    'taxes_and_fees' => $this->taxes_and_fees ?? null,
                    'uuid' => $this->uuid ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setOrderId(int $order_id): self
    {
        $this->order_id = $order_id;
        return $this;
    }

    public function getOrderId(): int
    {
        return $this->order_id;
    }

    public function setAccountId(int $account_id): self
    {
        $this->account_id = $account_id;
        return $this;
    }

    public function getAccountId(): int
    {
        return $this->account_id;
    }

    public function setInvoiceNumber(string $invoice_number): self
    {
        $this->invoice_number = $invoice_number;
        return $this;
    }

    public function getInvoiceNumber(): string
    {
        return $this->invoice_number;
    }

    public function setIssueDate(string $issue_date): self
    {
        $this->issue_date = $issue_date;
        return $this;
    }

    public function getIssueDate(): string
    {
        return $this->issue_date;
    }

    public function setDueDate(?string $due_date): self
    {
        $this->due_date = $due_date;
        return $this;
    }

    public function getDueDate(): ?string
    {
        return $this->due_date;
    }

    public function setTotalAmount(float $total_amount): self
    {
        $this->total_amount = $total_amount;
        return $this;
    }

    public function getTotalAmount(): float
    {
        return $this->total_amount;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setItems(array|string $items): self
    {
        $this->items = $items;
        return $this;
    }

    public function getItems(): array|string
    {
        return $this->items;
    }

    public function setTaxesAndFees(array|string|null $taxes_and_fees): self
    {
        $this->taxes_and_fees = $taxes_and_fees;
        return $this;
    }

    public function getTaxesAndFees(): array|string|null
    {
        return $this->taxes_and_fees;
    }

    public function setUuid(string $uuid): self
    {
        $this->uuid = $uuid;
        return $this;
    }

    public function getUuid(): string
    {
        return $this->uuid;
    }

    public function setCreatedAt(?string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): ?string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(?string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updated_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }
}
