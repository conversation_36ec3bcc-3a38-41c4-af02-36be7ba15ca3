<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class CapacityAssignmentDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'capacity_assignment';
    final public const PLURAL_NAME = 'capacity_assignments';
    final public const ID = 'id';
    final public const EVENT_ID = 'event_id';
    final public const NAME = 'name';
    final public const CAPACITY = 'capacity';
    final public const USED_CAPACITY = 'used_capacity';
    final public const APPLIES_TO = 'applies_to';
    final public const STATUS = 'status';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';
    final public const DELETED_AT = 'deleted_at';

    protected int $id;
    protected int $event_id;
    protected string $name;
    protected ?int $capacity = null;
    protected int $used_capacity = 0;
    protected string $applies_to = 'EVENT';
    protected string $status = 'ACTIVE';
    protected ?string $created_at = null;
    protected ?string $updated_at = null;
    protected ?string $deleted_at = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'event_id' => $this->event_id ?? null,
                    'name' => $this->name ?? null,
                    'capacity' => $this->capacity ?? null,
                    'used_capacity' => $this->used_capacity ?? null,
                    'applies_to' => $this->applies_to ?? null,
                    'status' => $this->status ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setEventId(int $event_id): self
    {
        $this->event_id = $event_id;
        return $this;
    }

    public function getEventId(): int
    {
        return $this->event_id;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setCapacity(?int $capacity): self
    {
        $this->capacity = $capacity;
        return $this;
    }

    public function getCapacity(): ?int
    {
        return $this->capacity;
    }

    public function setUsedCapacity(int $used_capacity): self
    {
        $this->used_capacity = $used_capacity;
        return $this;
    }

    public function getUsedCapacity(): int
    {
        return $this->used_capacity;
    }

    public function setAppliesTo(string $applies_to): self
    {
        $this->applies_to = $applies_to;
        return $this;
    }

    public function getAppliesTo(): string
    {
        return $this->applies_to;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setCreatedAt(?string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): ?string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(?string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updated_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }
}
