<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class StripePaymentDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'stripe_payment';
    final public const PLURAL_NAME = 'stripe_payments';
    final public const ID = 'id';
    final public const ORDER_ID = 'order_id';
    final public const PAYMENT_INTENT_ID = 'payment_intent_id';
    final public const CHARGE_ID = 'charge_id';
    final public const PAYMENT_METHOD_ID = 'payment_method_id';
    final public const AMOUNT_RECEIVED = 'amount_received';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';
    final public const DELETED_AT = 'deleted_at';
    final public const LAST_ERROR = 'last_error';
    final public const CONNECTED_ACCOUNT_ID = 'connected_account_id';
    final public const APPLICATION_FEE = 'application_fee';

    protected int $id;
    protected int $order_id;
    protected string $payment_intent_id;
    protected ?string $charge_id = null;
    protected ?string $payment_method_id = null;
    protected ?int $amount_received = null;
    protected ?string $created_at = null;
    protected ?string $updated_at = null;
    protected ?string $deleted_at = null;
    protected array|string|null $last_error = null;
    protected ?string $connected_account_id = null;
    protected int $application_fee = 0;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'order_id' => $this->order_id ?? null,
                    'payment_intent_id' => $this->payment_intent_id ?? null,
                    'charge_id' => $this->charge_id ?? null,
                    'payment_method_id' => $this->payment_method_id ?? null,
                    'amount_received' => $this->amount_received ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                    'last_error' => $this->last_error ?? null,
                    'connected_account_id' => $this->connected_account_id ?? null,
                    'application_fee' => $this->application_fee ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setOrderId(int $order_id): self
    {
        $this->order_id = $order_id;
        return $this;
    }

    public function getOrderId(): int
    {
        return $this->order_id;
    }

    public function setPaymentIntentId(string $payment_intent_id): self
    {
        $this->payment_intent_id = $payment_intent_id;
        return $this;
    }

    public function getPaymentIntentId(): string
    {
        return $this->payment_intent_id;
    }

    public function setChargeId(?string $charge_id): self
    {
        $this->charge_id = $charge_id;
        return $this;
    }

    public function getChargeId(): ?string
    {
        return $this->charge_id;
    }

    public function setPaymentMethodId(?string $payment_method_id): self
    {
        $this->payment_method_id = $payment_method_id;
        return $this;
    }

    public function getPaymentMethodId(): ?string
    {
        return $this->payment_method_id;
    }

    public function setAmountReceived(?int $amount_received): self
    {
        $this->amount_received = $amount_received;
        return $this;
    }

    public function getAmountReceived(): ?int
    {
        return $this->amount_received;
    }

    public function setCreatedAt(?string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): ?string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(?string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updated_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }

    public function setLastError(array|string|null $last_error): self
    {
        $this->last_error = $last_error;
        return $this;
    }

    public function getLastError(): array|string|null
    {
        return $this->last_error;
    }

    public function setConnectedAccountId(?string $connected_account_id): self
    {
        $this->connected_account_id = $connected_account_id;
        return $this;
    }

    public function getConnectedAccountId(): ?string
    {
        return $this->connected_account_id;
    }

    public function setApplicationFee(int $application_fee): self
    {
        $this->application_fee = $application_fee;
        return $this;
    }

    public function getApplicationFee(): int
    {
        return $this->application_fee;
    }
}
