alerts:
  - rule: DEPLOYMENT_FAILED
  - rule: DOMAIN_FAILED
databases:
  - cluster_name: hi-events-redis
    engine: REDIS
    name: hi-events-redis
    production: true
    version: "7"
  - cluster_name: hi-events-postgres
    db_name: hi-events-db
    db_user: hi-events-db
    engine: PG
    name: hi-events-postgres
    production: true
    version: "12"
domains:
  - domain: demo.hi.events
    type: PRIMARY
  - domain: app.hi.events
    type: ALIAS
envs:
  - key: APP_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
    value: EV[1:XXX:XXX]
  - key: APP_SAAS_MODE_ENABLED
    scope: RUN_AND_BUILD_TIME
    value: "true"
  - key: APP_SAAS_STRIPE_APPLICATION_FEE_PERCENT
    scope: RUN_AND_BUILD_TIME
    value: "1.5"
  - key: APP_FRONTEND_URL
    scope: RUN_AND_BUILD_TIME
    value: ${APP_URL}
  - key: APP_CDN_URL
    scope: RUN_AND_BUILD_TIME
    value: https://d31hxulcw8spzw.cloudfront.net
  - key: FILESYSTEM_DISK
    scope: RUN_AND_BUILD_TIME
    value: s3-public
  - key: JWT_SECRET
    scope: RUN_AND_BUILD_TIME
    type: SECRET
    value: EV[1:XXX:XXX]
  - key: LOG_CHANNEL
    scope: RUN_AND_BUILD_TIME
    value: stderr
  - key: AWS_ACCESS_KEY_ID
    scope: RUN_AND_BUILD_TIME
    type: SECRET
    value: EV[1:XXX:XXX]
  - key: AWS_SECRET_ACCESS_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
    value: EV[1:XXX:XXX]
  - key: AWS_DEFAULT_REGION
    scope: RUN_AND_BUILD_TIME
    value: us-west-1
  - key: AWS_PUBLIC_BUCKET
    scope: RUN_AND_BUILD_TIME
    value: hievents-public
  - key: AWS_PRIVATE_BUCKET
    scope: RUN_AND_BUILD_TIME
    value: hievents-private
  - key: STRIPE_PUBLIC_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
    value: EV[1:XXX:XXX]
  - key: STRIPE_SECRET_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
    value: EV[1:XXX:XXX]
  - key: MAIL_MAILER
    scope: RUN_AND_BUILD_TIME
    value: smtp
  - key: MAIL_HOST
    scope: RUN_AND_BUILD_TIME
    value: smtp-relay.brevo.com
  - key: MAIL_PORT
    scope: RUN_AND_BUILD_TIME
    value: "587"
  - key: DB_CONNECTION
    scope: RUN_AND_BUILD_TIME
    value: pgsql
  - key: DB_HOST
    scope: RUN_AND_BUILD_TIME
    value: ${hi-events-postgres.HOSTNAME}
  - key: DB_PORT
    scope: RUN_AND_BUILD_TIME
    value: ${hi-events-postgres.PORT}
  - key: DB_DATABASE
    scope: RUN_AND_BUILD_TIME
    value: ${hi-events-postgres.DATABASE}
  - key: DB_USERNAME
    scope: RUN_AND_BUILD_TIME
    value: ${hi-events-postgres.USERNAME}
  - key: DB_PASSWORD
    scope: RUN_AND_BUILD_TIME
    value: ${hi-events-postgres.PASSWORD}
  - key: REDIS_HOST
    scope: RUN_AND_BUILD_TIME
    value: ${hi-events-redis.HOSTNAME}
  - key: REDIS_PASSWORD
    scope: RUN_AND_BUILD_TIME
    value: ${hi-events-redis.PASSWORD}
  - key: REDIS_USER
    scope: RUN_AND_BUILD_TIME
    value: ${hi-events-redis.USERNAME}
  - key: REDIS_PORT
    scope: RUN_AND_BUILD_TIME
    value: ${hi-events-redis.PORT}
  - key: REDIS_URL
    scope: RUN_AND_BUILD_TIME
    value: ${hi-events-redis.REDIS_URL}
  - key: QUEUE_CONNECTION
    scope: RUN_AND_BUILD_TIME
    value: redis
  - key: DATABASE_URL
    scope: RUN_AND_BUILD_TIME
    value: ${hi-events-postgres.DATABASE_URL}
  - key: STRIPE_WEBHOOK_SECRET
    scope: RUN_AND_BUILD_TIME
    type: SECRET
    value: EV[1:XXX:XXX]
  - key: MAIL_USERNAME
    scope: RUN_AND_BUILD_TIME
    value: <EMAIL>
  - key: MAIL_PASSWORD
    scope: RUN_AND_BUILD_TIME
    value: XXX
  - key: MAIL_FROM_ADDRESS
    scope: RUN_AND_BUILD_TIME
    value: <EMAIL>
  - key: MAIL_FROM_NAME
    scope: RUN_AND_BUILD_TIME
    value: Hi.Events
features:
  - buildpack-stack=ubuntu-22
ingress:
  rules:
    - component:
        name: hi-events-backend
      match:
        path:
          prefix: /api
    - component:
        name: hi-events-frontend
      match:
        path:
          prefix: /
jobs:
  - dockerfile_path: /backend/Dockerfile
    github:
      branch: main
      deploy_on_push: true
      repo: HiEventsDev/Hi.Events
    instance_count: 2
    instance_size_slug: professional-xs
    kind: PRE_DEPLOY
    log_destinations:
      - logtail:
          token: XXX
        name: LogTail
    name: hi-events-migration
    run_command: php artisan migrate --force
    source_dir: backend
name: hi-events-backend-app
region: sfo
services:
  - alerts:
      - operator: GREATER_THAN
        rule: CPU_UTILIZATION
        value: 50
        window: FIVE_MINUTES
      - operator: GREATER_THAN
        rule: MEM_UTILIZATION
        value: 50
        window: FIVE_MINUTES
    dockerfile_path: /backend/Dockerfile
    github:
      branch: main
      deploy_on_push: true
      repo: HiEventsDev/Hi.Events
    http_port: 8080
    instance_count: 1
    instance_size_slug: professional-xs
    log_destinations:
      - logtail:
          token: XXX
        name: LogTail
    name: hi-events-backend
    source_dir: backend
  - build_command: yarn build
    environment_slug: node-js
    envs:
      - key: VITE_STRIPE_PUBLISHABLE_KEY
        scope: RUN_AND_BUILD_TIME
        value: pk_test_XXX
      - key: VITE_API_URL_SERVER
        scope: RUN_AND_BUILD_TIME
        value: ${APP_URL}/api
      - key: VITE_API_URL_CLIENT
        scope: RUN_AND_BUILD_TIME
        value: ${APP_URL}/api
      - key: VITE_FRONTEND_URL
        scope: RUN_AND_BUILD_TIME
        value: ${APP_URL}
    github:
      branch: main
      deploy_on_push: true
      repo: HiEventsDev/Hi.Events
    http_port: 5678
    instance_count: 1
    instance_size_slug: professional-xs
    name: hi-events-frontend
    run_command: yarn start
    source_dir: frontend
workers:
  - dockerfile_path: /backend/Dockerfile
    github:
      branch: main
      deploy_on_push: true
      repo: HiEventsDev/Hi.Events
    instance_count: 1
    instance_size_slug: professional-xs
    log_destinations:
      - logtail:
          token: XXX
        name: LogTail
    name: hi-events-worker
    run_command: php artisan queue:work
    source_dir: backend
