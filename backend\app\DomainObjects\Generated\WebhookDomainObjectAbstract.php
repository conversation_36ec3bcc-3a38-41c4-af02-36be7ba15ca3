<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class WebhookDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'webhook';
    final public const PLURAL_NAME = 'webhooks';
    final public const ID = 'id';
    final public const USER_ID = 'user_id';
    final public const EVENT_ID = 'event_id';
    final public const ACCOUNT_ID = 'account_id';
    final public const URL = 'url';
    final public const EVENT_TYPES = 'event_types';
    final public const LAST_RESPONSE_CODE = 'last_response_code';
    final public const LAST_RESPONSE_BODY = 'last_response_body';
    final public const LAST_TRIGGERED_AT = 'last_triggered_at';
    final public const STATUS = 'status';
    final public const SECRET = 'secret';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';
    final public const DELETED_AT = 'deleted_at';

    protected int $id;
    protected int $user_id;
    protected int $event_id;
    protected int $account_id;
    protected string $url;
    protected array|string $event_types;
    protected ?int $last_response_code = null;
    protected ?string $last_response_body = null;
    protected ?string $last_triggered_at = null;
    protected string $status = 'ENABLED';
    protected string $secret;
    protected ?string $created_at = null;
    protected ?string $updated_at = null;
    protected ?string $deleted_at = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'user_id' => $this->user_id ?? null,
                    'event_id' => $this->event_id ?? null,
                    'account_id' => $this->account_id ?? null,
                    'url' => $this->url ?? null,
                    'event_types' => $this->event_types ?? null,
                    'last_response_code' => $this->last_response_code ?? null,
                    'last_response_body' => $this->last_response_body ?? null,
                    'last_triggered_at' => $this->last_triggered_at ?? null,
                    'status' => $this->status ?? null,
                    'secret' => $this->secret ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setUserId(int $user_id): self
    {
        $this->user_id = $user_id;
        return $this;
    }

    public function getUserId(): int
    {
        return $this->user_id;
    }

    public function setEventId(int $event_id): self
    {
        $this->event_id = $event_id;
        return $this;
    }

    public function getEventId(): int
    {
        return $this->event_id;
    }

    public function setAccountId(int $account_id): self
    {
        $this->account_id = $account_id;
        return $this;
    }

    public function getAccountId(): int
    {
        return $this->account_id;
    }

    public function setUrl(string $url): self
    {
        $this->url = $url;
        return $this;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function setEventTypes(array|string $event_types): self
    {
        $this->event_types = $event_types;
        return $this;
    }

    public function getEventTypes(): array|string
    {
        return $this->event_types;
    }

    public function setLastResponseCode(?int $last_response_code): self
    {
        $this->last_response_code = $last_response_code;
        return $this;
    }

    public function getLastResponseCode(): ?int
    {
        return $this->last_response_code;
    }

    public function setLastResponseBody(?string $last_response_body): self
    {
        $this->last_response_body = $last_response_body;
        return $this;
    }

    public function getLastResponseBody(): ?string
    {
        return $this->last_response_body;
    }

    public function setLastTriggeredAt(?string $last_triggered_at): self
    {
        $this->last_triggered_at = $last_triggered_at;
        return $this;
    }

    public function getLastTriggeredAt(): ?string
    {
        return $this->last_triggered_at;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setSecret(string $secret): self
    {
        $this->secret = $secret;
        return $this;
    }

    public function getSecret(): string
    {
        return $this->secret;
    }

    public function setCreatedAt(?string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): ?string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(?string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updated_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }
}
