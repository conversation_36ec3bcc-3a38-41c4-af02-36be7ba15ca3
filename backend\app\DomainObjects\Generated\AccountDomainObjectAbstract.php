<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class AccountDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'account';
    final public const PLURAL_NAME = 'accounts';
    final public const ID = 'id';
    final public const ACCOUNT_CONFIGURATION_ID = 'account_configuration_id';
    final public const CURRENCY_CODE = 'currency_code';
    final public const TIMEZONE = 'timezone';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';
    final public const DELETED_AT = 'deleted_at';
    final public const NAME = 'name';
    final public const EMAIL = 'email';
    final public const STRIPE_ACCOUNT_ID = 'stripe_account_id';
    final public const SHORT_ID = 'short_id';
    final public const STRIPE_CONNECT_SETUP_COMPLETE = 'stripe_connect_setup_complete';
    final public const ACCOUNT_VERIFIED_AT = 'account_verified_at';
    final public const STRIPE_CONNECT_ACCOUNT_TYPE = 'stripe_connect_account_type';
    final public const IS_MANUALLY_VERIFIED = 'is_manually_verified';

    protected int $id;
    protected ?int $account_configuration_id = null;
    protected string $currency_code = 'USD';
    protected ?string $timezone = null;
    protected ?string $created_at = null;
    protected ?string $updated_at = null;
    protected ?string $deleted_at = null;
    protected string $name;
    protected string $email;
    protected ?string $stripe_account_id = null;
    protected string $short_id;
    protected ?bool $stripe_connect_setup_complete = false;
    protected ?string $account_verified_at = null;
    protected ?string $stripe_connect_account_type = null;
    protected bool $is_manually_verified = false;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'account_configuration_id' => $this->account_configuration_id ?? null,
                    'currency_code' => $this->currency_code ?? null,
                    'timezone' => $this->timezone ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                    'name' => $this->name ?? null,
                    'email' => $this->email ?? null,
                    'stripe_account_id' => $this->stripe_account_id ?? null,
                    'short_id' => $this->short_id ?? null,
                    'stripe_connect_setup_complete' => $this->stripe_connect_setup_complete ?? null,
                    'account_verified_at' => $this->account_verified_at ?? null,
                    'stripe_connect_account_type' => $this->stripe_connect_account_type ?? null,
                    'is_manually_verified' => $this->is_manually_verified ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setAccountConfigurationId(?int $account_configuration_id): self
    {
        $this->account_configuration_id = $account_configuration_id;
        return $this;
    }

    public function getAccountConfigurationId(): ?int
    {
        return $this->account_configuration_id;
    }

    public function setCurrencyCode(string $currency_code): self
    {
        $this->currency_code = $currency_code;
        return $this;
    }

    public function getCurrencyCode(): string
    {
        return $this->currency_code;
    }

    public function setTimezone(?string $timezone): self
    {
        $this->timezone = $timezone;
        return $this;
    }

    public function getTimezone(): ?string
    {
        return $this->timezone;
    }

    public function setCreatedAt(?string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): ?string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(?string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updated_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;
        return $this;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setStripeAccountId(?string $stripe_account_id): self
    {
        $this->stripe_account_id = $stripe_account_id;
        return $this;
    }

    public function getStripeAccountId(): ?string
    {
        return $this->stripe_account_id;
    }

    public function setShortId(string $short_id): self
    {
        $this->short_id = $short_id;
        return $this;
    }

    public function getShortId(): string
    {
        return $this->short_id;
    }

    public function setStripeConnectSetupComplete(?bool $stripe_connect_setup_complete): self
    {
        $this->stripe_connect_setup_complete = $stripe_connect_setup_complete;
        return $this;
    }

    public function getStripeConnectSetupComplete(): ?bool
    {
        return $this->stripe_connect_setup_complete;
    }

    public function setAccountVerifiedAt(?string $account_verified_at): self
    {
        $this->account_verified_at = $account_verified_at;
        return $this;
    }

    public function getAccountVerifiedAt(): ?string
    {
        return $this->account_verified_at;
    }

    public function setStripeConnectAccountType(?string $stripe_connect_account_type): self
    {
        $this->stripe_connect_account_type = $stripe_connect_account_type;
        return $this;
    }

    public function getStripeConnectAccountType(): ?string
    {
        return $this->stripe_connect_account_type;
    }

    public function setIsManuallyVerified(bool $is_manually_verified): self
    {
        $this->is_manually_verified = $is_manually_verified;
        return $this;
    }

    public function getIsManuallyVerified(): bool
    {
        return $this->is_manually_verified;
    }
}
