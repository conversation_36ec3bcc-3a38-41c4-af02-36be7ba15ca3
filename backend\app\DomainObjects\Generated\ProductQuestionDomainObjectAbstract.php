<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class ProductQuestionDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'product_question';
    final public const PLURAL_NAME = 'product_questions';
    final public const ID = 'id';
    final public const PRODUCT_ID = 'product_id';
    final public const QUESTION_ID = 'question_id';
    final public const DELETED_AT = 'deleted_at';

    protected int $id;
    protected int $product_id;
    protected int $question_id;
    protected ?string $deleted_at = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'product_id' => $this->product_id ?? null,
                    'question_id' => $this->question_id ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setProductId(int $product_id): self
    {
        $this->product_id = $product_id;
        return $this;
    }

    public function getProductId(): int
    {
        return $this->product_id;
    }

    public function setQuestionId(int $question_id): self
    {
        $this->question_id = $question_id;
        return $this;
    }

    public function getQuestionId(): int
    {
        return $this->question_id;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }
}
