<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class AccountConfigurationDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'account_configuration';
    final public const PLURAL_NAME = 'account_configurations';
    final public const ID = 'id';
    final public const NAME = 'name';
    final public const IS_SYSTEM_DEFAULT = 'is_system_default';
    final public const APPLICATION_FEES = 'application_fees';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';
    final public const DELETED_AT = 'deleted_at';

    protected int $id;
    protected string $name;
    protected bool $is_system_default = false;
    protected array|string|null $application_fees = null;
    protected ?string $created_at = null;
    protected ?string $updated_at = null;
    protected ?string $deleted_at = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'name' => $this->name ?? null,
                    'is_system_default' => $this->is_system_default ?? null,
                    'application_fees' => $this->application_fees ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setIsSystemDefault(bool $is_system_default): self
    {
        $this->is_system_default = $is_system_default;
        return $this;
    }

    public function getIsSystemDefault(): bool
    {
        return $this->is_system_default;
    }

    public function setApplicationFees(array|string|null $application_fees): self
    {
        $this->application_fees = $application_fees;
        return $this;
    }

    public function getApplicationFees(): array|string|null
    {
        return $this->application_fees;
    }

    public function setCreatedAt(?string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): ?string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(?string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updated_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }
}
