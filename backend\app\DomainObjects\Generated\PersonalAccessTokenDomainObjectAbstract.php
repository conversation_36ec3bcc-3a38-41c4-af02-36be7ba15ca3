<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class PersonalAccessTokenDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'personal_access_token';
    final public const PLURAL_NAME = 'personal_access_tokens';
    final public const ID = 'id';
    final public const TOKENABLE_TYPE = 'tokenable_type';
    final public const TOKENABLE_ID = 'tokenable_id';
    final public const NAME = 'name';
    final public const TOKEN = 'token';
    final public const ABILITIES = 'abilities';
    final public const LAST_USED_AT = 'last_used_at';
    final public const EXPIRES_AT = 'expires_at';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';

    protected int $id;
    protected string $tokenable_type;
    protected int $tokenable_id;
    protected string $name;
    protected string $token;
    protected ?string $abilities = null;
    protected ?string $last_used_at = null;
    protected ?string $expires_at = null;
    protected ?string $created_at = null;
    protected ?string $updated_at = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'tokenable_type' => $this->tokenable_type ?? null,
                    'tokenable_id' => $this->tokenable_id ?? null,
                    'name' => $this->name ?? null,
                    'token' => $this->token ?? null,
                    'abilities' => $this->abilities ?? null,
                    'last_used_at' => $this->last_used_at ?? null,
                    'expires_at' => $this->expires_at ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setTokenableType(string $tokenable_type): self
    {
        $this->tokenable_type = $tokenable_type;
        return $this;
    }

    public function getTokenableType(): string
    {
        return $this->tokenable_type;
    }

    public function setTokenableId(int $tokenable_id): self
    {
        $this->tokenable_id = $tokenable_id;
        return $this;
    }

    public function getTokenableId(): int
    {
        return $this->tokenable_id;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setToken(string $token): self
    {
        $this->token = $token;
        return $this;
    }

    public function getToken(): string
    {
        return $this->token;
    }

    public function setAbilities(?string $abilities): self
    {
        $this->abilities = $abilities;
        return $this;
    }

    public function getAbilities(): ?string
    {
        return $this->abilities;
    }

    public function setLastUsedAt(?string $last_used_at): self
    {
        $this->last_used_at = $last_used_at;
        return $this;
    }

    public function getLastUsedAt(): ?string
    {
        return $this->last_used_at;
    }

    public function setExpiresAt(?string $expires_at): self
    {
        $this->expires_at = $expires_at;
        return $this;
    }

    public function getExpiresAt(): ?string
    {
        return $this->expires_at;
    }

    public function setCreatedAt(?string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): ?string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(?string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updated_at;
    }
}
