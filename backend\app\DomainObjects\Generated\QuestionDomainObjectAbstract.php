<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class QuestionDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'question';
    final public const PLURAL_NAME = 'questions';
    final public const ID = 'id';
    final public const EVENT_ID = 'event_id';
    final public const TITLE = 'title';
    final public const REQUIRED = 'required';
    final public const TYPE = 'type';
    final public const OPTIONS = 'options';
    final public const BELONGS_TO = 'belongs_to';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';
    final public const DELETED_AT = 'deleted_at';
    final public const ORDER = 'order';
    final public const IS_HIDDEN = 'is_hidden';
    final public const DESCRIPTION = 'description';

    protected int $id;
    protected int $event_id;
    protected string $title;
    protected bool $required = false;
    protected ?string $type = null;
    protected array|string|null $options = null;
    protected string $belongs_to;
    protected string $created_at;
    protected string $updated_at;
    protected ?string $deleted_at = null;
    protected int $order = 1;
    protected bool $is_hidden = false;
    protected ?string $description = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'event_id' => $this->event_id ?? null,
                    'title' => $this->title ?? null,
                    'required' => $this->required ?? null,
                    'type' => $this->type ?? null,
                    'options' => $this->options ?? null,
                    'belongs_to' => $this->belongs_to ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                    'order' => $this->order ?? null,
                    'is_hidden' => $this->is_hidden ?? null,
                    'description' => $this->description ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setEventId(int $event_id): self
    {
        $this->event_id = $event_id;
        return $this;
    }

    public function getEventId(): int
    {
        return $this->event_id;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;
        return $this;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setRequired(bool $required): self
    {
        $this->required = $required;
        return $this;
    }

    public function getRequired(): bool
    {
        return $this->required;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setOptions(array|string|null $options): self
    {
        $this->options = $options;
        return $this;
    }

    public function getOptions(): array|string|null
    {
        return $this->options;
    }

    public function setBelongsTo(string $belongs_to): self
    {
        $this->belongs_to = $belongs_to;
        return $this;
    }

    public function getBelongsTo(): string
    {
        return $this->belongs_to;
    }

    public function setCreatedAt(string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): string
    {
        return $this->updated_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }

    public function setOrder(int $order): self
    {
        $this->order = $order;
        return $this;
    }

    public function getOrder(): int
    {
        return $this->order;
    }

    public function setIsHidden(bool $is_hidden): self
    {
        $this->is_hidden = $is_hidden;
        return $this;
    }

    public function getIsHidden(): bool
    {
        return $this->is_hidden;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }
}
