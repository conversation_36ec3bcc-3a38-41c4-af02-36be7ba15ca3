<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class AttendeeDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'attendee';
    final public const PLURAL_NAME = 'attendees';
    final public const ID = 'id';
    final public const ORDER_ID = 'order_id';
    final public const PRODUCT_ID = 'product_id';
    final public const EVENT_ID = 'event_id';
    final public const CHECKED_IN_BY = 'checked_in_by';
    final public const CHECKED_OUT_BY = 'checked_out_by';
    final public const PRODUCT_PRICE_ID = 'product_price_id';
    final public const SHORT_ID = 'short_id';
    final public const FIRST_NAME = 'first_name';
    final public const LAST_NAME = 'last_name';
    final public const EMAIL = 'email';
    final public const PUBLIC_ID = 'public_id';
    final public const STATUS = 'status';
    final public const CHECKED_IN_AT = 'checked_in_at';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';
    final public const DELETED_AT = 'deleted_at';
    final public const LOCALE = 'locale';
    final public const NOTES = 'notes';

    protected int $id;
    protected int $order_id;
    protected int $product_id;
    protected int $event_id;
    protected ?int $checked_in_by = null;
    protected ?int $checked_out_by = null;
    protected int $product_price_id;
    protected string $short_id;
    protected string $first_name = '';
    protected string $last_name = '';
    protected string $email;
    protected string $public_id;
    protected string $status;
    protected ?string $checked_in_at = null;
    protected string $created_at;
    protected string $updated_at;
    protected ?string $deleted_at = null;
    protected string $locale = 'en';
    protected ?string $notes = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'order_id' => $this->order_id ?? null,
                    'product_id' => $this->product_id ?? null,
                    'event_id' => $this->event_id ?? null,
                    'checked_in_by' => $this->checked_in_by ?? null,
                    'checked_out_by' => $this->checked_out_by ?? null,
                    'product_price_id' => $this->product_price_id ?? null,
                    'short_id' => $this->short_id ?? null,
                    'first_name' => $this->first_name ?? null,
                    'last_name' => $this->last_name ?? null,
                    'email' => $this->email ?? null,
                    'public_id' => $this->public_id ?? null,
                    'status' => $this->status ?? null,
                    'checked_in_at' => $this->checked_in_at ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                    'locale' => $this->locale ?? null,
                    'notes' => $this->notes ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setOrderId(int $order_id): self
    {
        $this->order_id = $order_id;
        return $this;
    }

    public function getOrderId(): int
    {
        return $this->order_id;
    }

    public function setProductId(int $product_id): self
    {
        $this->product_id = $product_id;
        return $this;
    }

    public function getProductId(): int
    {
        return $this->product_id;
    }

    public function setEventId(int $event_id): self
    {
        $this->event_id = $event_id;
        return $this;
    }

    public function getEventId(): int
    {
        return $this->event_id;
    }

    public function setCheckedInBy(?int $checked_in_by): self
    {
        $this->checked_in_by = $checked_in_by;
        return $this;
    }

    public function getCheckedInBy(): ?int
    {
        return $this->checked_in_by;
    }

    public function setCheckedOutBy(?int $checked_out_by): self
    {
        $this->checked_out_by = $checked_out_by;
        return $this;
    }

    public function getCheckedOutBy(): ?int
    {
        return $this->checked_out_by;
    }

    public function setProductPriceId(int $product_price_id): self
    {
        $this->product_price_id = $product_price_id;
        return $this;
    }

    public function getProductPriceId(): int
    {
        return $this->product_price_id;
    }

    public function setShortId(string $short_id): self
    {
        $this->short_id = $short_id;
        return $this;
    }

    public function getShortId(): string
    {
        return $this->short_id;
    }

    public function setFirstName(string $first_name): self
    {
        $this->first_name = $first_name;
        return $this;
    }

    public function getFirstName(): string
    {
        return $this->first_name;
    }

    public function setLastName(string $last_name): self
    {
        $this->last_name = $last_name;
        return $this;
    }

    public function getLastName(): string
    {
        return $this->last_name;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;
        return $this;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setPublicId(string $public_id): self
    {
        $this->public_id = $public_id;
        return $this;
    }

    public function getPublicId(): string
    {
        return $this->public_id;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setCheckedInAt(?string $checked_in_at): self
    {
        $this->checked_in_at = $checked_in_at;
        return $this;
    }

    public function getCheckedInAt(): ?string
    {
        return $this->checked_in_at;
    }

    public function setCreatedAt(string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): string
    {
        return $this->updated_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }

    public function setLocale(string $locale): self
    {
        $this->locale = $locale;
        return $this;
    }

    public function getLocale(): string
    {
        return $this->locale;
    }

    public function setNotes(?string $notes): self
    {
        $this->notes = $notes;
        return $this;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }
}
