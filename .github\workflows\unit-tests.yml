name: Run Unit Tests

on:
  push:
    branches: [main, develop]
    paths:
      - 'backend/**'
  pull_request:
    paths:
      - 'backend/**'

jobs:
  run-tests:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-versions: ['8.2', '8.3', '8.4']

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: mbstring, xml, ctype, iconv, intl, pdo, pdo_mysql, tokenizer
          ini-values: post_max_size=256M, upload_max_filesize=256M
          coverage: none

      - name: Get Composer Cache Directory
        id: composer-cache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            ${{ runner.os }}-composer-

      - name: Create <PERSON>vel bootstrap cache directory
        run: mkdir -p ./backend/bootstrap/cache && chmod -R 777 ./backend/bootstrap/cache

      - name: Install dependencies
        run: cd backend && composer install --prefer-dist --no-progress --no-interaction

      - name: Run PHPUnit Tests
        run: cd backend && ./vendor/bin/phpunit tests/Unit --no-coverage
