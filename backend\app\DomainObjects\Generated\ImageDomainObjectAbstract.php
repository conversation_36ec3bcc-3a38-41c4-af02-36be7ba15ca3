<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class ImageDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'image';
    final public const PLURAL_NAME = 'images';
    final public const ID = 'id';
    final public const ENTITY_ID = 'entity_id';
    final public const ENTITY_TYPE = 'entity_type';
    final public const TYPE = 'type';
    final public const FILENAME = 'filename';
    final public const DISK = 'disk';
    final public const PATH = 'path';
    final public const SIZE = 'size';
    final public const MIME_TYPE = 'mime_type';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';
    final public const DELETED_AT = 'deleted_at';

    protected int $id;
    protected ?int $entity_id = null;
    protected ?string $entity_type = null;
    protected ?string $type = null;
    protected ?string $filename = null;
    protected ?string $disk = null;
    protected ?string $path = null;
    protected ?int $size = null;
    protected ?string $mime_type = null;
    protected ?string $created_at = 'CURRENT_TIMESTAMP';
    protected ?string $updated_at = 'CURRENT_TIMESTAMP';
    protected ?string $deleted_at = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'entity_id' => $this->entity_id ?? null,
                    'entity_type' => $this->entity_type ?? null,
                    'type' => $this->type ?? null,
                    'filename' => $this->filename ?? null,
                    'disk' => $this->disk ?? null,
                    'path' => $this->path ?? null,
                    'size' => $this->size ?? null,
                    'mime_type' => $this->mime_type ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setEntityId(?int $entity_id): self
    {
        $this->entity_id = $entity_id;
        return $this;
    }

    public function getEntityId(): ?int
    {
        return $this->entity_id;
    }

    public function setEntityType(?string $entity_type): self
    {
        $this->entity_type = $entity_type;
        return $this;
    }

    public function getEntityType(): ?string
    {
        return $this->entity_type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setFilename(?string $filename): self
    {
        $this->filename = $filename;
        return $this;
    }

    public function getFilename(): ?string
    {
        return $this->filename;
    }

    public function setDisk(?string $disk): self
    {
        $this->disk = $disk;
        return $this;
    }

    public function getDisk(): ?string
    {
        return $this->disk;
    }

    public function setPath(?string $path): self
    {
        $this->path = $path;
        return $this;
    }

    public function getPath(): ?string
    {
        return $this->path;
    }

    public function setSize(?int $size): self
    {
        $this->size = $size;
        return $this;
    }

    public function getSize(): ?int
    {
        return $this->size;
    }

    public function setMimeType(?string $mime_type): self
    {
        $this->mime_type = $mime_type;
        return $this;
    }

    public function getMimeType(): ?string
    {
        return $this->mime_type;
    }

    public function setCreatedAt(?string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): ?string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(?string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updated_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }
}
