<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class AffiliateDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'affiliate';
    final public const PLURAL_NAME = 'affiliates';
    final public const ID = 'id';
    final public const EVENT_ID = 'event_id';
    final public const CODE = 'code';
    final public const SALES_VOLUME = 'sales_volume';
    final public const UNIQUE_VISITORS = 'unique_visitors';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';
    final public const DELETED_AT = 'deleted_at';

    protected int $id;
    protected ?int $event_id = null;
    protected string $code;
    protected ?float $sales_volume = null;
    protected int $unique_visitors = 0;
    protected string $created_at;
    protected ?string $updated_at = null;
    protected ?string $deleted_at = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'event_id' => $this->event_id ?? null,
                    'code' => $this->code ?? null,
                    'sales_volume' => $this->sales_volume ?? null,
                    'unique_visitors' => $this->unique_visitors ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setEventId(?int $event_id): self
    {
        $this->event_id = $event_id;
        return $this;
    }

    public function getEventId(): ?int
    {
        return $this->event_id;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;
        return $this;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function setSalesVolume(?float $sales_volume): self
    {
        $this->sales_volume = $sales_volume;
        return $this;
    }

    public function getSalesVolume(): ?float
    {
        return $this->sales_volume;
    }

    public function setUniqueVisitors(int $unique_visitors): self
    {
        $this->unique_visitors = $unique_visitors;
        return $this;
    }

    public function getUniqueVisitors(): int
    {
        return $this->unique_visitors;
    }

    public function setCreatedAt(string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(?string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updated_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }
}
