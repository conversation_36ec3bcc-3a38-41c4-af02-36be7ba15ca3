name: Publish Docker image

on:
  release:
    types: [published]
  workflow_dispatch:

jobs:
  push_to_registry:
    name: Push Docker images to Docker Hub
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          persist-credentials: true

      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      # All-in-one Image Steps
      - name: Extract metadata (tags, labels) for All-in-one Docker
        id: meta_all_in_one
        uses: docker/metadata-action@v3
        with:
          images: daveearley/hi.events-all-in-one

      - name: Build and push All-in-one Docker image
        uses: docker/build-push-action@v3
        with:
          context: ./
          file: ./Dockerfile.all-in-one
          push: true
          tags: ${{ steps.meta_all_in_one.outputs.tags }}
          labels: ${{ steps.meta_all_in_one.outputs.labels }}

      # Backend Image Steps
      - name: Extract metadata (tags, labels) for Backend Docker
        id: meta_backend
        uses: docker/metadata-action@v3
        with:
          images: daveearley/hi.events-backend

      - name: Build and push Backend Docker image
        uses: docker/build-push-action@v3
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: ${{ steps.meta_backend.outputs.tags }}
          labels: ${{ steps.meta_backend.outputs.labels }}

      # Frontend Image Steps
      - name: Extract metadata (tags, labels) for Frontend Docker
        id: meta_frontend
        uses: docker/metadata-action@v3
        with:
          images: daveearley/hi.events-frontend

      - name: Build and push Frontend Docker image
        uses: docker/build-push-action@v3
        with:
          context: ./frontend
          file: ./frontend/Dockerfile.ssr
          push: true
          tags: ${{ steps.meta_frontend.outputs.tags }}
          labels: ${{ steps.meta_frontend.outputs.labels }}
