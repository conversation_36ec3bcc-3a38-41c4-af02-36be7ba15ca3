Thank you for creating a PR! We appreciate your contribution to Hi.Events.

To make the process as smooth as possible, please ensure you've read the [contributing guidelines](https://github.com/HiEventsDev/hi.events/blob/develop/CONTRIBUTING.md) before proceeding.

Please include a summary of the changes and the issue being fixed or the feature being added. The more detail, the better!

## Checklist

- [ ] I have read the contributing guidelines.
- [ ] My code is of good quality and follows the coding standards of the project.
- [ ] I have tested my changes, and they work as expected.

Thank you for your contribution! 🎉
