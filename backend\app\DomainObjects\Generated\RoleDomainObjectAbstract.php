<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class RoleDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'role';
    final public const PLURAL_NAME = 'roles';
    final public const ID = 'id';
    final public const ACCOUNT_ID = 'account_id';
    final public const NAME = 'name';
    final public const PERMISSIONS = 'permissions';

    protected int $id;
    protected ?int $account_id = null;
    protected ?string $name = null;
    protected array|string $permissions;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'account_id' => $this->account_id ?? null,
                    'name' => $this->name ?? null,
                    'permissions' => $this->permissions ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setAccountId(?int $account_id): self
    {
        $this->account_id = $account_id;
        return $this;
    }

    public function getAccountId(): ?int
    {
        return $this->account_id;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setPermissions(array|string $permissions): self
    {
        $this->permissions = $permissions;
        return $this;
    }

    public function getPermissions(): array|string
    {
        return $this->permissions;
    }
}
