<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class OrderApplicationFeeDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'order_application_fee';
    final public const PLURAL_NAME = 'order_application_fees';
    final public const ID = 'id';
    final public const ORDER_ID = 'order_id';
    final public const AMOUNT = 'amount';
    final public const CURRENCY = 'currency';
    final public const STATUS = 'status';
    final public const PAYMENT_METHOD = 'payment_method';
    final public const METADATA = 'metadata';
    final public const PAID_AT = 'paid_at';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';
    final public const DELETED_AT = 'deleted_at';

    protected int $id;
    protected int $order_id;
    protected float $amount;
    protected string $currency = 'USD';
    protected string $status;
    protected string $payment_method;
    protected array|string|null $metadata = null;
    protected ?string $paid_at = null;
    protected ?string $created_at = null;
    protected ?string $updated_at = null;
    protected ?string $deleted_at = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'order_id' => $this->order_id ?? null,
                    'amount' => $this->amount ?? null,
                    'currency' => $this->currency ?? null,
                    'status' => $this->status ?? null,
                    'payment_method' => $this->payment_method ?? null,
                    'metadata' => $this->metadata ?? null,
                    'paid_at' => $this->paid_at ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setOrderId(int $order_id): self
    {
        $this->order_id = $order_id;
        return $this;
    }

    public function getOrderId(): int
    {
        return $this->order_id;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;
        return $this;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setPaymentMethod(string $payment_method): self
    {
        $this->payment_method = $payment_method;
        return $this;
    }

    public function getPaymentMethod(): string
    {
        return $this->payment_method;
    }

    public function setMetadata(array|string|null $metadata): self
    {
        $this->metadata = $metadata;
        return $this;
    }

    public function getMetadata(): array|string|null
    {
        return $this->metadata;
    }

    public function setPaidAt(?string $paid_at): self
    {
        $this->paid_at = $paid_at;
        return $this;
    }

    public function getPaidAt(): ?string
    {
        return $this->paid_at;
    }

    public function setCreatedAt(?string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): ?string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(?string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updated_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }
}
