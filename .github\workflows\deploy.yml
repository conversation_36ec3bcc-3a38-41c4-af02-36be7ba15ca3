name: Deploy to Vapor and Frontend

on:
  push:
    branches:
      - main
      - develop
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      test_mode:
        description: 'Run in test mode (no actual deployment)'
        required: false
        default: false
        type: boolean

jobs:
  backend:
    name: Deploy Backend
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3
          tools: composer:v2
          coverage: none

      - name: Prepare Laravel Environment
        working-directory: ./backend
        run: |
          mkdir -p bootstrap/cache
          chmod -R 775 bootstrap/cache

      - name: Prepare HTMLPurifier Cache Directory
        working-directory: ./backend
        run: |
          mkdir -p storage/app/htmlpurifier
          chmod -R 775 storage/app/htmlpurifier

      - name: Install Dependencies
        working-directory: ./backend
        run: composer install --no-dev --no-progress --no-scripts --optimize-autoloader

      - name: Install Vapor CLI
        run: composer global require laravel/vapor-cli

      - name: Set Deployment Environment
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "VAPOR_ENV=${{ github.event.inputs.environment }}" >> "$GITHUB_ENV"
            echo "TEST_MODE=${{ github.event.inputs.test_mode }}" >> "$GITHUB_ENV"
          elif [[ "${{ github.ref_name }}" == "develop" ]]; then
            echo "VAPOR_ENV=staging" >> "$GITHUB_ENV"
            echo "TEST_MODE=false" >> "$GITHUB_ENV"
          else
            echo "VAPOR_ENV=production" >> "$GITHUB_ENV"
            echo "TEST_MODE=false" >> "$GITHUB_ENV"
          fi

      - name: Log Branch and Environment
        run: |
          echo "🚀 Deploying to Vapor environment: ${{ env.VAPOR_ENV }}"
          echo "🧪 Test mode: ${{ env.TEST_MODE }}"

      - name: Validate Deployment Configuration
        working-directory: ./backend
        run: |
          if [[ "${{ env.TEST_MODE }}" == "true" ]]; then
            echo "✅ TEST MODE: Would deploy to ${{ env.VAPOR_ENV }} environment"
            echo "vapor deploy ${{ env.VAPOR_ENV }} --dry-run"
            exit 0
          fi

      - name: Deploy to Vapor
        working-directory: ./backend
        run: vapor deploy ${{ env.VAPOR_ENV }}
        env:
          VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}

  frontend:
    name: Deploy Frontend
    runs-on: ubuntu-latest
    needs: backend

    steps:
      - uses: actions/checkout@v3

      - name: Set Deployment Environment
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            if [[ "${{ github.event.inputs.environment }}" == "staging" ]]; then
              echo "DO_APP_ID=${{ secrets.DIGITALOCEAN_STAGING_APP_ID }}" >> "$GITHUB_ENV"
            else
              echo "DO_APP_ID=${{ secrets.DIGITALOCEAN_PRODUCTION_APP_ID }}" >> "$GITHUB_ENV"
            fi
            echo "TEST_MODE=${{ github.event.inputs.test_mode }}" >> "$GITHUB_ENV"
          elif [[ "${{ github.ref_name }}" == "develop" ]]; then
            echo "DO_APP_ID=${{ secrets.DIGITALOCEAN_STAGING_APP_ID }}" >> "$GITHUB_ENV"
            echo "TEST_MODE=false" >> "$GITHUB_ENV"
          else
            echo "DO_APP_ID=${{ secrets.DIGITALOCEAN_PRODUCTION_APP_ID }}" >> "$GITHUB_ENV"
            echo "TEST_MODE=false" >> "$GITHUB_ENV"
          fi

      - name: Log Environment Settings
        run: |
          echo "🚀 Deploying frontend to DigitalOcean App: ${{ env.DO_APP_ID }}"
          echo "🧪 Test mode: ${{ env.TEST_MODE }}"

      - name: Validate Deployment Configuration (Test Mode)
        if: env.TEST_MODE == 'true'
        run: |
          echo "✅ TEST MODE: Would trigger deployment for DigitalOcean App: ${{ env.DO_APP_ID }}"
          echo "curl -X POST 'https://api.digitalocean.com/v2/apps/${{ env.DO_APP_ID }}/deployments'"
          exit 0

      - name: Trigger Deployment on DigitalOcean
        if: env.TEST_MODE != 'true'
        id: trigger_deployment
        run: |
          RESPONSE=$(curl -s -o response.json -w "%{http_code}" -X POST "https://api.digitalocean.com/v2/apps/$DO_APP_ID/deployments" \
            -H "Authorization: Bearer ${{ secrets.DIGITALOCEAN_API_TOKEN }}" \
            -H "Content-Type: application/json")

          if [ "$RESPONSE" -ne 201 ] && [ "$RESPONSE" -ne 200 ]; then
            ERROR_MSG=$(jq -r '.message // "Unknown error occurred."' response.json)
            echo "❌ Failed to trigger deployment. HTTP Status: $RESPONSE. Error: $ERROR_MSG"
            exit 1
          fi

          DEPLOYMENT_ID=$(jq -r '.deployment.id' response.json)
          if [ "$DEPLOYMENT_ID" == "null" ]; then
            echo "❌ Failed to extract deployment ID."
            exit 1
          fi

          echo "::add-mask::$DEPLOYMENT_ID"
          echo "✅ Deployment triggered successfully."

          echo "deployment_id=$DEPLOYMENT_ID" >> "$GITHUB_ENV"

      - name: Poll Deployment Status
        if: env.TEST_MODE != 'true'
        run: |
          MAX_RETRIES=60
          SLEEP_TIME=10
          COUNTER=0

          while [ $COUNTER -lt $MAX_RETRIES ]; do
            RESPONSE=$(curl -s -X GET "https://api.digitalocean.com/v2/apps/$DO_APP_ID/deployments/${{ env.deployment_id }}" \
              -H "Authorization: Bearer ${{ secrets.DIGITALOCEAN_API_TOKEN }}" \
              -H "Content-Type: application/json")

            STATUS=$(echo "$RESPONSE" | jq -r '.deployment.phase')

            echo "🔄 Deployment Status: $STATUS"

            if [ "$STATUS" == "ACTIVE" ]; then
              echo "✅ Deployment completed successfully."
              exit 0
            elif [[ "$STATUS" == "FAILED" || "$STATUS" == "CANCELED" ]]; then
              echo "❌ Deployment failed or was cancelled."
              exit 1
            fi

            COUNTER=$((COUNTER + 1))
            echo "⏳ Retrying in $SLEEP_TIME seconds... ($COUNTER/$MAX_RETRIES)"
            sleep $SLEEP_TIME
          done

          echo "⏰ Deployment timed out."
          exit 1
