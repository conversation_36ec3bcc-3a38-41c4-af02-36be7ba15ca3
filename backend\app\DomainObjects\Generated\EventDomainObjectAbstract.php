<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class EventDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'event';
    final public const PLURAL_NAME = 'events';
    final public const ID = 'id';
    final public const ACCOUNT_ID = 'account_id';
    final public const USER_ID = 'user_id';
    final public const ORGANIZER_ID = 'organizer_id';
    final public const TITLE = 'title';
    final public const START_DATE = 'start_date';
    final public const END_DATE = 'end_date';
    final public const DESCRIPTION = 'description';
    final public const STATUS = 'status';
    final public const LOCATION_DETAILS = 'location_details';
    final public const CURRENCY = 'currency';
    final public const TIMEZONE = 'timezone';
    final public const ATTRIBUTES = 'attributes';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';
    final public const DELETED_AT = 'deleted_at';
    final public const LOCATION = 'location';
    final public const SHORT_ID = 'short_id';
    final public const TICKET_QUANTITY_AVAILABLE = 'ticket_quantity_available';

    protected int $id;
    protected int $account_id;
    protected int $user_id;
    protected ?int $organizer_id = null;
    protected string $title;
    protected ?string $start_date = null;
    protected ?string $end_date = null;
    protected ?string $description = null;
    protected ?string $status = null;
    protected array|string|null $location_details = null;
    protected string $currency = 'USD';
    protected ?string $timezone = null;
    protected array|string|null $attributes = null;
    protected string $created_at;
    protected string $updated_at;
    protected ?string $deleted_at = null;
    protected ?string $location = null;
    protected string $short_id;
    protected ?int $ticket_quantity_available = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'account_id' => $this->account_id ?? null,
                    'user_id' => $this->user_id ?? null,
                    'organizer_id' => $this->organizer_id ?? null,
                    'title' => $this->title ?? null,
                    'start_date' => $this->start_date ?? null,
                    'end_date' => $this->end_date ?? null,
                    'description' => $this->description ?? null,
                    'status' => $this->status ?? null,
                    'location_details' => $this->location_details ?? null,
                    'currency' => $this->currency ?? null,
                    'timezone' => $this->timezone ?? null,
                    'attributes' => $this->attributes ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                    'location' => $this->location ?? null,
                    'short_id' => $this->short_id ?? null,
                    'ticket_quantity_available' => $this->ticket_quantity_available ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setAccountId(int $account_id): self
    {
        $this->account_id = $account_id;
        return $this;
    }

    public function getAccountId(): int
    {
        return $this->account_id;
    }

    public function setUserId(int $user_id): self
    {
        $this->user_id = $user_id;
        return $this;
    }

    public function getUserId(): int
    {
        return $this->user_id;
    }

    public function setOrganizerId(?int $organizer_id): self
    {
        $this->organizer_id = $organizer_id;
        return $this;
    }

    public function getOrganizerId(): ?int
    {
        return $this->organizer_id;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;
        return $this;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setStartDate(?string $start_date): self
    {
        $this->start_date = $start_date;
        return $this;
    }

    public function getStartDate(): ?string
    {
        return $this->start_date;
    }

    public function setEndDate(?string $end_date): self
    {
        $this->end_date = $end_date;
        return $this;
    }

    public function getEndDate(): ?string
    {
        return $this->end_date;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setLocationDetails(array|string|null $location_details): self
    {
        $this->location_details = $location_details;
        return $this;
    }

    public function getLocationDetails(): array|string|null
    {
        return $this->location_details;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setTimezone(?string $timezone): self
    {
        $this->timezone = $timezone;
        return $this;
    }

    public function getTimezone(): ?string
    {
        return $this->timezone;
    }

    public function setAttributes(array|string|null $attributes): self
    {
        $this->attributes = $attributes;
        return $this;
    }

    public function getAttributes(): array|string|null
    {
        return $this->attributes;
    }

    public function setCreatedAt(string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): string
    {
        return $this->updated_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }

    public function setLocation(?string $location): self
    {
        $this->location = $location;
        return $this;
    }

    public function getLocation(): ?string
    {
        return $this->location;
    }

    public function setShortId(string $short_id): self
    {
        $this->short_id = $short_id;
        return $this;
    }

    public function getShortId(): string
    {
        return $this->short_id;
    }

    public function setTicketQuantityAvailable(?int $ticket_quantity_available): self
    {
        $this->ticket_quantity_available = $ticket_quantity_available;
        return $this;
    }

    public function getTicketQuantityAvailable(): ?int
    {
        return $this->ticket_quantity_available;
    }
}
