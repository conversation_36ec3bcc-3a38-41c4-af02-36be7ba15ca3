<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class WebhookLogDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'webhook_log';
    final public const PLURAL_NAME = 'webhook_logs';
    final public const ID = 'id';
    final public const WEBHOOK_ID = 'webhook_id';
    final public const PAYLOAD = 'payload';
    final public const EVENT_TYPE = 'event_type';
    final public const RESPONSE_CODE = 'response_code';
    final public const RESPONSE_BODY = 'response_body';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';
    final public const DELETED_AT = 'deleted_at';

    protected int $id;
    protected int $webhook_id;
    protected string $payload;
    protected string $event_type;
    protected ?int $response_code = null;
    protected ?string $response_body = null;
    protected ?string $created_at = null;
    protected ?string $updated_at = null;
    protected ?string $deleted_at = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'webhook_id' => $this->webhook_id ?? null,
                    'payload' => $this->payload ?? null,
                    'event_type' => $this->event_type ?? null,
                    'response_code' => $this->response_code ?? null,
                    'response_body' => $this->response_body ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setWebhookId(int $webhook_id): self
    {
        $this->webhook_id = $webhook_id;
        return $this;
    }

    public function getWebhookId(): int
    {
        return $this->webhook_id;
    }

    public function setPayload(string $payload): self
    {
        $this->payload = $payload;
        return $this;
    }

    public function getPayload(): string
    {
        return $this->payload;
    }

    public function setEventType(string $event_type): self
    {
        $this->event_type = $event_type;
        return $this;
    }

    public function getEventType(): string
    {
        return $this->event_type;
    }

    public function setResponseCode(?int $response_code): self
    {
        $this->response_code = $response_code;
        return $this;
    }

    public function getResponseCode(): ?int
    {
        return $this->response_code;
    }

    public function setResponseBody(?string $response_body): self
    {
        $this->response_body = $response_body;
        return $this;
    }

    public function getResponseBody(): ?string
    {
        return $this->response_body;
    }

    public function setCreatedAt(?string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): ?string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(?string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updated_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }
}
