<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class CheckInListDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'check_in_list';
    final public const PLURAL_NAME = 'check_in_lists';
    final public const ID = 'id';
    final public const EVENT_ID = 'event_id';
    final public const SHORT_ID = 'short_id';
    final public const NAME = 'name';
    final public const DESCRIPTION = 'description';
    final public const EXPIRES_AT = 'expires_at';
    final public const ACTIVATES_AT = 'activates_at';
    final public const DELETED_AT = 'deleted_at';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';

    protected int $id;
    protected int $event_id;
    protected string $short_id;
    protected string $name;
    protected ?string $description = null;
    protected ?string $expires_at = null;
    protected ?string $activates_at = null;
    protected ?string $deleted_at = null;
    protected ?string $created_at = null;
    protected ?string $updated_at = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'event_id' => $this->event_id ?? null,
                    'short_id' => $this->short_id ?? null,
                    'name' => $this->name ?? null,
                    'description' => $this->description ?? null,
                    'expires_at' => $this->expires_at ?? null,
                    'activates_at' => $this->activates_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setEventId(int $event_id): self
    {
        $this->event_id = $event_id;
        return $this;
    }

    public function getEventId(): int
    {
        return $this->event_id;
    }

    public function setShortId(string $short_id): self
    {
        $this->short_id = $short_id;
        return $this;
    }

    public function getShortId(): string
    {
        return $this->short_id;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setExpiresAt(?string $expires_at): self
    {
        $this->expires_at = $expires_at;
        return $this;
    }

    public function getExpiresAt(): ?string
    {
        return $this->expires_at;
    }

    public function setActivatesAt(?string $activates_at): self
    {
        $this->activates_at = $activates_at;
        return $this;
    }

    public function getActivatesAt(): ?string
    {
        return $this->activates_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }

    public function setCreatedAt(?string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): ?string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(?string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updated_at;
    }
}
