<?php

namespace HiEvents\DomainObjects\Generated;

/**
 * THIS FILE IS AUTOGENERATED - DO NOT EDIT IT DIRECTLY.
 * @package HiEvents\DomainObjects\Generated
 */
abstract class EventLogDomainObjectAbstract extends \HiEvents\DomainObjects\AbstractDomainObject
{
    final public const SINGULAR_NAME = 'event_log';
    final public const PLURAL_NAME = 'event_logs';
    final public const ID = 'id';
    final public const USER_ID = 'user_id';
    final public const TYPE = 'type';
    final public const ENTITY_ID = 'entity_id';
    final public const ENTITY_TYPE = 'entity_type';
    final public const IP_ADDRESS = 'ip_address';
    final public const USER_AGENT = 'user_agent';
    final public const DATA = 'data';
    final public const CREATED_AT = 'created_at';
    final public const UPDATED_AT = 'updated_at';
    final public const DELETED_AT = 'deleted_at';

    protected int $id;
    protected int $user_id;
    protected string $type;
    protected int $entity_id;
    protected int $entity_type;
    protected ?string $ip_address = null;
    protected ?string $user_agent = null;
    protected array|string|null $data = null;
    protected string $created_at;
    protected ?string $updated_at = null;
    protected ?string $deleted_at = null;

    public function toArray(): array
    {
        return [
                    'id' => $this->id ?? null,
                    'user_id' => $this->user_id ?? null,
                    'type' => $this->type ?? null,
                    'entity_id' => $this->entity_id ?? null,
                    'entity_type' => $this->entity_type ?? null,
                    'ip_address' => $this->ip_address ?? null,
                    'user_agent' => $this->user_agent ?? null,
                    'data' => $this->data ?? null,
                    'created_at' => $this->created_at ?? null,
                    'updated_at' => $this->updated_at ?? null,
                    'deleted_at' => $this->deleted_at ?? null,
                ];
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setUserId(int $user_id): self
    {
        $this->user_id = $user_id;
        return $this;
    }

    public function getUserId(): int
    {
        return $this->user_id;
    }

    public function setType(string $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setEntityId(int $entity_id): self
    {
        $this->entity_id = $entity_id;
        return $this;
    }

    public function getEntityId(): int
    {
        return $this->entity_id;
    }

    public function setEntityType(int $entity_type): self
    {
        $this->entity_type = $entity_type;
        return $this;
    }

    public function getEntityType(): int
    {
        return $this->entity_type;
    }

    public function setIpAddress(?string $ip_address): self
    {
        $this->ip_address = $ip_address;
        return $this;
    }

    public function getIpAddress(): ?string
    {
        return $this->ip_address;
    }

    public function setUserAgent(?string $user_agent): self
    {
        $this->user_agent = $user_agent;
        return $this;
    }

    public function getUserAgent(): ?string
    {
        return $this->user_agent;
    }

    public function setData(array|string|null $data): self
    {
        $this->data = $data;
        return $this;
    }

    public function getData(): array|string|null
    {
        return $this->data;
    }

    public function setCreatedAt(string $created_at): self
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getCreatedAt(): string
    {
        return $this->created_at;
    }

    public function setUpdatedAt(?string $updated_at): self
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updated_at;
    }

    public function setDeletedAt(?string $deleted_at): self
    {
        $this->deleted_at = $deleted_at;
        return $this;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deleted_at;
    }
}
